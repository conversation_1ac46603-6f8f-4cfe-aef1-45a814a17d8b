/**
 * 模态框管理Hook
 * 管理各种模态框和抽屉的状态
 */

import { useState, useCallback } from 'react';
import type { ModalState, DrawerState } from '../types';

export interface UseModalReturn {
  /** 模态框状态 */
  modalState: ModalState;
  /** 显示模态框方法 */
  showModal: (config?: Partial<ModalState>) => void;
  /** 隐藏模态框方法 */
  hideModal: () => void;
  /** 设置模态框加载状态方法 */
  setModalLoading: (loading: boolean) => void;
}

export interface UseDrawerReturn {
  /** 抽屉状态 */
  drawerState: DrawerState;
  /** 显示抽屉方法 */
  showDrawer: (config?: Partial<DrawerState>) => void;
  /** 隐藏抽屉方法 */
  hideDrawer: () => void;
  /** 设置抽屉加载状态方法 */
  setDrawerLoading: (loading: boolean) => void;
}

/**
 * 模态框管理Hook
 */
export function useModal(initialState: Partial<ModalState> = {}): UseModalReturn {
  const [modalState, setModalState] = useState<ModalState>({
    visible: false,
    loading: false,
    ...initialState,
  });

  // 显示模态框方法
  const showModal = useCallback((config: Partial<ModalState> = {}) => {
    setModalState(prev => ({
      ...prev,
      visible: true,
      ...config,
    }));
  }, []);

  // 隐藏模态框方法
  const hideModal = useCallback(() => {
    setModalState(prev => ({
      ...prev,
      visible: false,
      loading: false,
    }));
  }, []);

  // 设置模态框加载状态方法
  const setModalLoading = useCallback((loading: boolean) => {
    setModalState(prev => ({
      ...prev,
      loading,
    }));
  }, []);

  return {
    modalState,
    showModal,
    hideModal,
    setModalLoading,
  };
}

/**
 * 抽屉管理Hook
 */
export function useDrawer(initialState: Partial<DrawerState> = {}): UseDrawerReturn {
  const [drawerState, setDrawerState] = useState<DrawerState>({
    visible: false,
    loading: false,
    placement: 'right',
    ...initialState,
  });

  // 显示抽屉方法
  const showDrawer = useCallback((config: Partial<DrawerState> = {}) => {
    setDrawerState(prev => ({
      ...prev,
      visible: true,
      ...config,
    }));
  }, []);

  // 隐藏抽屉方法
  const hideDrawer = useCallback(() => {
    setDrawerState(prev => ({
      ...prev,
      visible: false,
      loading: false,
    }));
  }, []);

  // 设置抽屉加载状态方法
  const setDrawerLoading = useCallback((loading: boolean) => {
    setDrawerState(prev => ({
      ...prev,
      loading,
    }));
  }, []);

  return {
    drawerState,
    showDrawer,
    hideDrawer,
    setDrawerLoading,
  };
}

/**
 * 多模态框管理Hook
 * 用于管理多个不同类型的模态框
 */
export function useMultiModal<T extends string>(
  modalTypes: T[]
): Record<T, UseModalReturn> {
  const modals = {} as Record<T, UseModalReturn>;

  modalTypes.forEach(type => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    modals[type] = useModal();
  });

  return modals;
}

/**
 * 多抽屉管理Hook
 * 用于管理多个不同类型的抽屉
 */
export function useMultiDrawer<T extends string>(
  drawerTypes: T[]
): Record<T, UseDrawerReturn> {
  const drawers = {} as Record<T, UseDrawerReturn>;

  drawerTypes.forEach(type => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    drawers[type] = useDrawer();
  });

  return drawers;
}
