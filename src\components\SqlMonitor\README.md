# SqlMonitor 模块

SQL监控任务管理模块，提供完整的任务管理功能。

## 目录结构

```
src/components/SqlMonitor/
├── components/           # 组件目录
│   ├── common/          # 通用组件
│   │   └── TaskGroupSelect.tsx
│   ├── AntdTable.tsx    # 主表格组件
│   ├── ComplexTaskForm.tsx  # 复合表单组件
│   ├── GroupManagementModal.tsx  # 分组管理模态框
│   ├── TaskFormModals.tsx  # 基础表单模态框
│   ├── TaskFormModalsExtended.tsx  # 扩展表单模态框
│   └── index.ts         # 组件导出
├── constants/           # 常量配置
│   ├── form.ts         # 表单相关常量
│   ├── pagination.ts   # 分页相关常量
│   ├── table.ts        # 表格相关常量
│   ├── task.ts         # 任务相关常量
│   └── index.ts        # 常量导出
├── hooks/              # 自定义钩子
│   ├── useModal.ts     # 模态框管理
│   ├── useTable.ts     # 表格管理
│   ├── useTaskData.ts  # 任务数据管理
│   ├── useTaskForm.ts  # 表单管理
│   ├── useTaskSelection.ts  # 选择管理
│   └── index.ts        # hooks导出
├── services/           # 服务层
│   ├── httpClient.ts   # HTTP客户端
│   ├── mockData.ts     # 模拟数据
│   ├── taskService.ts  # 任务服务
│   └── index.ts        # 服务导出
├── styles/             # 样式文件
│   ├── common.module.css    # 通用样式
│   ├── form.module.css      # 表单样式
│   ├── modal.module.css     # 模态框样式
│   ├── table.module.css     # 表格样式
│   └── index.ts            # 样式导出
├── types/              # 类型定义
│   ├── api.ts          # API相关类型
│   ├── form.ts         # 表单相关类型
│   ├── modal.ts        # 模态框相关类型
│   ├── table.ts        # 表格相关类型
│   ├── task.ts         # 任务相关类型
│   └── index.ts        # 类型导出
├── index.ts            # 模块主入口
└── README.md           # 说明文档
```

## 使用方式

### 基础使用

```tsx
import { AntdTable } from '@/components/SqlMonitor';

function App() {
  return (
    <div style={{ height: '100vh' }}>
      <AntdTable contentHeight={800} />
    </div>
  );
}
```

### 使用自定义hooks

```tsx
import { useTaskData, useTaskSelection } from '@/components/SqlMonitor';

function CustomComponent() {
  const { data, loading, loadData } = useTaskData();
  const { selection, rowSelection } = useTaskSelection(data);
  
  // 自定义逻辑
}
```

### 使用服务

```tsx
import { TaskService } from '@/components/SqlMonitor';

async function fetchTasks() {
  const response = await TaskService.getTasks({
    current: 1,
    page_size: 15,
  });
  return response.data;
}
```

## 主要特性

- **模块化设计**: 按功能分离，便于维护和扩展
- **类型安全**: 完整的TypeScript类型定义
- **自定义hooks**: 可复用的业务逻辑
- **统一样式**: CSS模块化管理
- **服务层抽离**: API调用统一管理
- **常量配置**: 配置项集中管理

## 重构说明

本模块是从原有的分散组件重构而来，主要改进：

1. **结构优化**: 按功能模块组织代码
2. **逻辑抽离**: 使用自定义hooks管理状态和逻辑
3. **类型完善**: 完整的TypeScript类型系统
4. **样式统一**: CSS模块化，避免样式冲突
5. **服务分离**: API调用与组件解耦
6. **常量管理**: 配置项统一管理，便于维护

## 依赖关系

- React 18+
- Ant Design 5+
- TypeScript 4.5+
- dayjs (时间处理)

## 注意事项

1. 确保项目中已安装所需依赖
2. 样式文件使用CSS模块，需要配置相应的构建工具
3. 部分功能依赖于项目现有的工具函数（如频率转换器）
4. 模拟数据仅用于开发测试，生产环境需要连接真实API
