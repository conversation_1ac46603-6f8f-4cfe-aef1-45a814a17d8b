import { DeleteOutlined, PlusOutlined, TeamOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import React from 'react';

import { tableStyles } from '../styles';

interface ActionButtonsProps {
  selectedCount: number;
  onAddTask: () => void;
  onGroupManage: () => void;
  onBatchDelete: () => void;
  onClearSelection: () => void;
}

/**
 * 操作按钮组件
 * 包含新增任务、分组管理、批量操作等功能
 */
export const ActionButtons: React.FC<ActionButtonsProps> = ({
  selectedCount,
  onAddTask,
  onGroupManage,
  onBatchDelete,
  onClearSelection,
}) => {
  return (
    <div className='flex-shrink-0 h-12 flex items-center justify-between px-4 bg-white border-t border-gray-200'>
      {/* 左侧：新增任务按钮和分组管理按钮 */}
      <div className='flex items-center gap-2'>
        <Button
          type='primary'
          size='middle'
          icon={<PlusOutlined />}
          onClick={onAddTask}
          className={`${tableStyles.addTaskBtn} w-40 h-8 flex items-center justify-center`}
        >
          新增任务
        </Button>
        <Button
          type='primary'
          size='middle'
          icon={<TeamOutlined />}
          onClick={onGroupManage}
          className={`${tableStyles.groupManageBtn} w-40 h-8 flex items-center justify-center`}
        >
          分组管理
        </Button>
      </div>

      {/* 右侧：批量操作区域 - 当有选中项时显示 */}
      {selectedCount > 0 && (
        <div className={tableStyles.batchOperations}>
          <div className='flex items-center gap-4 h-full'>
            <div className='flex items-center gap-2'>
              <div className={tableStyles.pulseDot}></div>
              <span className='text-blue-700 font-medium'>已选择 {selectedCount} 项（跨页面选择）</span>
            </div>
            <div className='flex items-center gap-2'>
              <Button 
                type='text' 
                danger 
                onClick={onBatchDelete} 
                className={tableStyles.batchDeleteBtn} 
                icon={<DeleteOutlined />}
              >
                批量删除
              </Button>
              <Button 
                type='text' 
                onClick={onClearSelection} 
                className={tableStyles.batchCancelBtn}
              >
                取消全选
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
