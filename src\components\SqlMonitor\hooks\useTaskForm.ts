/**
 * 任务表单管理Hook
 * 管理表单的状态、验证、提交等逻辑
 */

import { useState, useCallback } from 'react';
import { Form } from 'antd';
import type { FormInstance } from 'antd/es/form';
import type { TaskBasic, FormAction, FormState } from '../types';
import { TaskService } from '../services';

export interface UseTaskFormOptions {
  /** 表单操作类型 */
  action?: FormAction;
  /** 初始数据 */
  initialData?: TaskBasic;
  /** 提交成功回调 */
  onSuccess?: (data: any) => void;
  /** 提交失败回调 */
  onError?: (error: any) => void;
}

export interface UseTaskFormReturn {
  /** 表单实例 */
  form: FormInstance;
  /** 表单状态 */
  formState: FormState;
  /** 是否为编辑模式 */
  isEditMode: boolean;
  /** 提交表单方法 */
  handleSubmit: (values: any) => Promise<void>;
  /** 重置表单方法 */
  handleReset: () => void;
  /** 设置表单数据方法 */
  setFormData: (data: Partial<TaskBasic>) => void;
  /** 获取表单数据方法 */
  getFormData: () => any;
  /** 验证表单方法 */
  validateForm: () => Promise<boolean>;
}

/**
 * 任务表单管理Hook
 */
export function useTaskForm(options: UseTaskFormOptions = {}): UseTaskFormReturn {
  const { action = 'add', initialData, onSuccess, onError } = options;
  const [form] = Form.useForm();
  const [formState, setFormState] = useState<FormState>('idle');

  // 是否为编辑模式
  const isEditMode = action === 'edit' && !!initialData;

  // 提交表单方法
  const handleSubmit = useCallback(
    async (values: any) => {
      setFormState('submitting');

      try {
        let result;
        
        if (isEditMode && initialData) {
          // 编辑模式
          result = await TaskService.updateComplexForm(initialData.id, {
            id: initialData.id,
            ...values,
          });
        } else {
          // 新增模式
          result = await TaskService.saveComplexForm(values);
        }

        setFormState('success');
        onSuccess?.(result);
      } catch (error) {
        setFormState('error');
        onError?.(error);
        console.error('表单提交失败:', error);
      }
    },
    [isEditMode, initialData, onSuccess, onError]
  );

  // 重置表单方法
  const handleReset = useCallback(() => {
    form.resetFields();
    setFormState('idle');
  }, [form]);

  // 设置表单数据方法
  const setFormData = useCallback(
    (data: Partial<TaskBasic>) => {
      form.setFieldsValue(data);
    },
    [form]
  );

  // 获取表单数据方法
  const getFormData = useCallback(() => {
    return form.getFieldsValue();
  }, [form]);

  // 验证表单方法
  const validateForm = useCallback(async (): Promise<boolean> => {
    try {
      await form.validateFields();
      return true;
    } catch (error) {
      console.error('表单验证失败:', error);
      return false;
    }
  }, [form]);

  return {
    form,
    formState,
    isEditMode,
    handleSubmit,
    handleReset,
    setFormData,
    getFormData,
    validateForm,
  };
}
