/**
 * Prettier 代码格式化配置
 *
 * 此配置文件定义了项目中代码格式化的规则
 * 主要目标：让对象属性每一项都换行，提高代码可读性
 */

/** @type {import("prettier").Config} */
const config = {
  // ==================== 基础格式化选项 ====================

  /**
   * 语句末尾是否添加分号
   * true: 总是添加分号 (推荐)
   * false: 仅在必要时添加分号
   */
  semi: true,

  /**
   * 多行时是否在最后一个元素后添加逗号
   * "none": 不添加尾随逗号
   * "es5": 在 ES5 中有效的地方添加尾随逗号（对象、数组等）
   * "all": 尽可能添加尾随逗号（包括函数参数）
   */
  trailingComma: 'es5',

  /**
   * 是否使用单引号
   * true: 使用单引号 '
   * false: 使用双引号 "
   */
  singleQuote: true,

  /**
   * 每行最大字符数
   * 设置为 140 可以在现代宽屏显示器上更好地利用空间
   * 同时保持代码的可读性
   */
  printWidth: 100,

  /**
   * 缩进空格数
   * 2: 使用 2 个空格缩进（推荐，节省空间）
   * 4: 使用 4 个空格缩进
   */
  tabWidth: 2,

  /**
   * 是否使用制表符缩进
   * false: 使用空格缩进（推荐，跨平台兼容性更好）
   * true: 使用制表符缩进
   */
  useTabs: false,

  // ==================== 对象和数组格式化 ====================

  /**
   * 对象字面量的大括号内是否添加空格
   * true: { foo: bar } （推荐，提高可读性）
   * false: {foo: bar}
   */
  bracketSpacing: true,

  /**
   * 多行 JSX 元素的 > 是否放在最后一行的末尾
   * false: > 放在新行（推荐）
   * true: > 放在最后一行末尾
   */
  bracketSameLine: false,

  // ==================== 箭头函数和 JSX ====================

  /**
   * 箭头函数参数是否总是添加括号
   * "avoid": 尽可能避免括号 x => x
   * "always": 总是添加括号 (x) => x
   */
  arrowParens: 'avoid',

  /**
   * JSX 中是否使用单引号
   * true: 使用单引号（与 singleQuote 保持一致）
   * false: 使用双引号
   */
  jsxSingleQuote: true,

  // ==================== 其他选项 ====================

  /**
   * 换行符类型
   * "lf": 仅使用 \n（Unix/Linux 风格，推荐）
   * "crlf": 使用 \r\n（Windows 风格）
   * "cr": 仅使用 \r（旧 Mac 风格）
   * "auto": 保持现有换行符
   */
  endOfLine: 'lf',

  /**
   * 对象属性的引号使用策略
   * "as-needed": 仅在需要时添加引号（推荐）
   * "consistent": 要么全部加引号，要么全部不加
   * "preserve": 保持原有的引号使用方式
   */
  quoteProps: 'as-needed',

  /**
   * Markdown 文本换行方式
   * "preserve": 保持原有换行（推荐）
   * "always": 总是换行
   * "never": 从不换行
   */
  proseWrap: 'preserve',

  /**
   * HTML 空白字符敏感性
   * "css": 遵循 CSS display 属性的默认值（推荐）
   * "strict": 所有标签周围的空白字符都被认为是重要的
   * "ignore": 所有标签周围的空白字符都被认为是不重要的
   */
  htmlWhitespaceSensitivity: 'css',

  /**
   * 嵌入式语言格式化
   * "auto": 自动格式化嵌入的代码（推荐）
   * "off": 不格式化嵌入的代码
   */
  embeddedLanguageFormatting: 'auto',

  // ==================== 特定文件类型的覆盖配置 ====================

  /**
   * 针对特定文件类型的格式化覆盖规则
   * 可以为不同类型的文件设置不同的格式化选项
   */
  overrides: [
    {
      // JSON 文件配置
      files: ['*.json', '*.jsonc'],
      options: {
        // JSON 文件使用较小的行宽，让对象属性更容易换行
        printWidth: 80,
        // JSON 文件使用 2 个空格缩进
        tabWidth: 2,
      },
    },
    {
      // Markdown 文件配置
      files: ['*.md', '*.mdx'],
      options: {
        // Markdown 文件使用较大的行宽
        printWidth: 100,
        // 保持原有的换行方式
        proseWrap: 'preserve',
      },
    },
    {
      // YAML 文件配置
      files: ['*.yml', '*.yaml'],
      options: {
        // YAML 文件使用 2 个空格缩进
        tabWidth: 2,
        // 使用单引号
        singleQuote: true,
      },
    },
  ],
};

// 使用 ES 模块导出
export default config;
